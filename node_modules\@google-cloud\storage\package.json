{"name": "@google-cloud/storage", "description": "Cloud Storage Client Library for Node.js", "version": "7.16.0", "license": "Apache-2.0", "author": "Google Inc.", "engines": {"node": ">=14"}, "repository": "googleapis/nodejs-storage", "main": "./build/cjs/src/index.js", "types": "./build/cjs/src/index.d.ts", "type": "module", "exports": {".": {"import": {"types": "./build/esm/src/index.d.ts", "default": "./build/esm/src/index.js"}, "require": {"types": "./build/cjs/src/index.d.ts", "default": "./build/cjs/src/index.js"}}}, "files": ["build/cjs/src", "build/cjs/package.json", "!build/cjs/src/**/*.map", "build/esm/src", "!build/esm/src/**/*.map"], "keywords": ["google apis client", "google api client", "google apis", "google api", "google", "google cloud platform", "google cloud", "cloud", "google storage", "storage"], "scripts": {"all-test": "npm test && npm run system-test && npm run samples-test", "benchwrapper": "node bin/benchwrapper.js", "check": "gts check", "clean": "rm -rf build/", "compile:cjs": "tsc -p ./tsconfig.cjs.json", "compile:esm": "tsc -p .", "compile": "npm run compile:cjs && npm run compile:esm", "conformance-test": "mocha --parallel build/cjs/conformance-test/ --require build/cjs/conformance-test/globalHooks.js", "docs-test": "linkinator docs", "docs": "jsdoc -c .jsdoc.json", "fix": "gts fix", "lint": "gts check", "postcompile": "cp ./src/package-json-helper.cjs ./build/cjs/src && cp ./src/package-json-helper.cjs ./build/esm/src", "postcompile:cjs": "babel --plugins gapic-tools/build/src/replaceImportMetaUrl,gapic-tools/build/src/toggleESMFlagVariable build/cjs/src/util.js -o build/cjs/src/util.js && cp internal-tooling/helpers/package.cjs.json build/cjs/package.json", "precompile": "rm -rf build/", "preconformance-test": "npm run compile:cjs -- --sourceMap", "predocs-test": "npm run docs", "predocs": "npm run compile:cjs -- --sourceMap", "prelint": "cd samples; npm link ../; npm install", "prepare": "npm run compile", "presystem-test:esm": "npm run compile:esm", "presystem-test": "npm run compile -- --sourceMap", "pretest": "npm run compile -- --sourceMap", "samples-test": "npm link && cd samples/ && npm link ../ && npm test && cd ../", "system-test:esm": "mocha build/esm/system-test --timeout 600000 --exit", "system-test": "mocha build/cjs/system-test --timeout 600000 --exit", "test": "c8 mocha build/cjs/test"}, "dependencies": {"@google-cloud/paginator": "^5.0.0", "@google-cloud/projectify": "^4.0.0", "@google-cloud/promisify": "<4.1.0", "abort-controller": "^3.0.0", "async-retry": "^1.3.3", "duplexify": "^4.1.3", "fast-xml-parser": "^4.4.1", "gaxios": "^6.0.2", "google-auth-library": "^9.6.3", "html-entities": "^2.5.2", "mime": "^3.0.0", "p-limit": "^3.0.1", "retry-request": "^7.0.0", "teeny-request": "^9.0.0", "uuid": "^8.0.0"}, "devDependencies": {"@babel/cli": "^7.22.10", "@babel/core": "^7.22.11", "@google-cloud/pubsub": "^4.0.0", "@grpc/grpc-js": "^1.0.3", "@grpc/proto-loader": "^0.7.0", "@types/async-retry": "^1.4.3", "@types/duplexify": "^3.6.4", "@types/mime": "^3.0.0", "@types/mocha": "^9.1.1", "@types/mockery": "^1.4.29", "@types/node": "^22.0.0", "@types/node-fetch": "^2.1.3", "@types/proxyquire": "^1.3.28", "@types/request": "^2.48.4", "@types/sinon": "^17.0.0", "@types/tmp": "0.2.6", "@types/uuid": "^8.0.0", "@types/yargs": "^17.0.10", "c8": "^9.0.0", "form-data": "^4.0.0", "gapic-tools": "^0.4.0", "gts": "^5.0.0", "jsdoc": "^4.0.0", "jsdoc-fresh": "^3.0.0", "jsdoc-region-tag": "^3.0.0", "linkinator": "^3.0.0", "mocha": "^9.2.2", "mockery": "^2.1.0", "nock": "~13.5.0", "node-fetch": "^2.6.7", "pack-n-play": "^2.0.0", "proxyquire": "^2.1.3", "sinon": "^18.0.0", "nise": "6.0.0", "path-to-regexp": "6.3.0", "tmp": "^0.2.0", "typescript": "^5.1.6", "yargs": "^17.3.1"}}