// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.firestore.v1;

import "google/firestore/v1/bloom_filter.proto";
import "google/firestore/v1/common.proto";
import "google/firestore/v1/document.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Firestore.V1";
option go_package = "cloud.google.com/go/firestore/apiv1/firestorepb;firestorepb";
option java_multiple_files = true;
option java_outer_classname = "WriteProto";
option java_package = "com.google.firestore.v1";
option objc_class_prefix = "GCFS";
option php_namespace = "Google\\Cloud\\Firestore\\V1";
option ruby_package = "Google::Cloud::Firestore::V1";

// A write on a document.
message Write {
  // The operation to execute.
  oneof operation {
    // A document to write.
    Document update = 1;

    // A document name to delete. In the format:
    // `projects/{project_id}/databases/{database_id}/documents/{document_path}`.
    string delete = 2;

    // Applies a transformation to a document.
    DocumentTransform transform = 6;
  }

  // The fields to update in this write.
  //
  // This field can be set only when the operation is `update`.
  // If the mask is not set for an `update` and the document exists, any
  // existing data will be overwritten.
  // If the mask is set and the document on the server has fields not covered by
  // the mask, they are left unchanged.
  // Fields referenced in the mask, but not present in the input document, are
  // deleted from the document on the server.
  // The field paths in this mask must not contain a reserved field name.
  DocumentMask update_mask = 3;

  // The transforms to perform after update.
  //
  // This field can be set only when the operation is `update`. If present, this
  // write is equivalent to performing `update` and `transform` to the same
  // document atomically and in order.
  repeated DocumentTransform.FieldTransform update_transforms = 7;

  // An optional precondition on the document.
  //
  // The write will fail if this is set and not met by the target document.
  Precondition current_document = 4;
}

// A transformation of a document.
message DocumentTransform {
  // A transformation of a field of the document.
  message FieldTransform {
    // A value that is calculated by the server.
    enum ServerValue {
      // Unspecified. This value must not be used.
      SERVER_VALUE_UNSPECIFIED = 0;

      // The time at which the server processed the request, with millisecond
      // precision. If used on multiple fields (same or different documents) in
      // a transaction, all the fields will get the same server timestamp.
      REQUEST_TIME = 1;
    }

    // The path of the field. See
    // [Document.fields][google.firestore.v1.Document.fields] for the field path
    // syntax reference.
    string field_path = 1;

    // The transformation to apply on the field.
    oneof transform_type {
      // Sets the field to the given server value.
      ServerValue set_to_server_value = 2;

      // Adds the given value to the field's current value.
      //
      // This must be an integer or a double value.
      // If the field is not an integer or double, or if the field does not yet
      // exist, the transformation will set the field to the given value.
      // If either of the given value or the current field value are doubles,
      // both values will be interpreted as doubles. Double arithmetic and
      // representation of double values follow IEEE 754 semantics.
      // If there is positive/negative integer overflow, the field is resolved
      // to the largest magnitude positive/negative integer.
      Value increment = 3;

      // Sets the field to the maximum of its current value and the given value.
      //
      // This must be an integer or a double value.
      // If the field is not an integer or double, or if the field does not yet
      // exist, the transformation will set the field to the given value.
      // If a maximum operation is applied where the field and the input value
      // are of mixed types (that is - one is an integer and one is a double)
      // the field takes on the type of the larger operand. If the operands are
      // equivalent (e.g. 3 and 3.0), the field does not change.
      // 0, 0.0, and -0.0 are all zero. The maximum of a zero stored value and
      // zero input value is always the stored value.
      // The maximum of any numeric value x and NaN is NaN.
      Value maximum = 4;

      // Sets the field to the minimum of its current value and the given value.
      //
      // This must be an integer or a double value.
      // If the field is not an integer or double, or if the field does not yet
      // exist, the transformation will set the field to the input value.
      // If a minimum operation is applied where the field and the input value
      // are of mixed types (that is - one is an integer and one is a double)
      // the field takes on the type of the smaller operand. If the operands are
      // equivalent (e.g. 3 and 3.0), the field does not change.
      // 0, 0.0, and -0.0 are all zero. The minimum of a zero stored value and
      // zero input value is always the stored value.
      // The minimum of any numeric value x and NaN is NaN.
      Value minimum = 5;

      // Append the given elements in order if they are not already present in
      // the current field value.
      // If the field is not an array, or if the field does not yet exist, it is
      // first set to the empty array.
      //
      // Equivalent numbers of different types (e.g. 3L and 3.0) are
      // considered equal when checking if a value is missing.
      // NaN is equal to NaN, and Null is equal to Null.
      // If the input contains multiple equivalent values, only the first will
      // be considered.
      //
      // The corresponding transform_result will be the null value.
      ArrayValue append_missing_elements = 6;

      // Remove all of the given elements from the array in the field.
      // If the field is not an array, or if the field does not yet exist, it is
      // set to the empty array.
      //
      // Equivalent numbers of the different types (e.g. 3L and 3.0) are
      // considered equal when deciding whether an element should be removed.
      // NaN is equal to NaN, and Null is equal to Null.
      // This will remove all equivalent values if there are duplicates.
      //
      // The corresponding transform_result will be the null value.
      ArrayValue remove_all_from_array = 7;
    }
  }

  // The name of the document to transform.
  string document = 1;

  // The list of transformations to apply to the fields of the document, in
  // order.
  // This must not be empty.
  repeated FieldTransform field_transforms = 2;
}

// The result of applying a write.
message WriteResult {
  // The last update time of the document after applying the write. Not set
  // after a `delete`.
  //
  // If the write did not actually change the document, this will be the
  // previous update_time.
  google.protobuf.Timestamp update_time = 1;

  // The results of applying each
  // [DocumentTransform.FieldTransform][google.firestore.v1.DocumentTransform.FieldTransform],
  // in the same order.
  repeated Value transform_results = 2;
}

// A [Document][google.firestore.v1.Document] has changed.
//
// May be the result of multiple [writes][google.firestore.v1.Write], including
// deletes, that ultimately resulted in a new value for the
// [Document][google.firestore.v1.Document].
//
// Multiple [DocumentChange][google.firestore.v1.DocumentChange] messages may be
// returned for the same logical change, if multiple targets are affected.
message DocumentChange {
  // The new state of the [Document][google.firestore.v1.Document].
  //
  // If `mask` is set, contains only fields that were updated or added.
  Document document = 1;

  // A set of target IDs of targets that match this document.
  repeated int32 target_ids = 5;

  // A set of target IDs for targets that no longer match this document.
  repeated int32 removed_target_ids = 6;
}

// A [Document][google.firestore.v1.Document] has been deleted.
//
// May be the result of multiple [writes][google.firestore.v1.Write], including
// updates, the last of which deleted the
// [Document][google.firestore.v1.Document].
//
// Multiple [DocumentDelete][google.firestore.v1.DocumentDelete] messages may be
// returned for the same logical delete, if multiple targets are affected.
message DocumentDelete {
  // The resource name of the [Document][google.firestore.v1.Document] that was
  // deleted.
  string document = 1;

  // A set of target IDs for targets that previously matched this entity.
  repeated int32 removed_target_ids = 6;

  // The read timestamp at which the delete was observed.
  //
  // Greater or equal to the `commit_time` of the delete.
  google.protobuf.Timestamp read_time = 4;
}

// A [Document][google.firestore.v1.Document] has been removed from the view of
// the targets.
//
// Sent if the document is no longer relevant to a target and is out of view.
// Can be sent instead of a DocumentDelete or a DocumentChange if the server
// can not send the new value of the document.
//
// Multiple [DocumentRemove][google.firestore.v1.DocumentRemove] messages may be
// returned for the same logical write or delete, if multiple targets are
// affected.
message DocumentRemove {
  // The resource name of the [Document][google.firestore.v1.Document] that has
  // gone out of view.
  string document = 1;

  // A set of target IDs for targets that previously matched this document.
  repeated int32 removed_target_ids = 2;

  // The read timestamp at which the remove was observed.
  //
  // Greater or equal to the `commit_time` of the change/delete/remove.
  google.protobuf.Timestamp read_time = 4;
}

// A digest of all the documents that match a given target.
message ExistenceFilter {
  // The target ID to which this filter applies.
  int32 target_id = 1;

  // The total count of documents that match
  // [target_id][google.firestore.v1.ExistenceFilter.target_id].
  //
  // If different from the count of documents in the client that match, the
  // client must manually determine which documents no longer match the target.
  //
  // The client can use the `unchanged_names` bloom filter to assist with
  // this determination by testing ALL the document names against the filter;
  // if the document name is NOT in the filter, it means the document no
  // longer matches the target.
  int32 count = 2;

  // A bloom filter that, despite its name, contains the UTF-8 byte encodings of
  // the resource names of ALL the documents that match
  // [target_id][google.firestore.v1.ExistenceFilter.target_id], in the form
  // `projects/{project_id}/databases/{database_id}/documents/{document_path}`.
  //
  // This bloom filter may be omitted at the server's discretion, such as if it
  // is deemed that the client will not make use of it or if it is too
  // computationally expensive to calculate or transmit. Clients must gracefully
  // handle this field being absent by falling back to the logic used before
  // this field existed; that is, re-add the target without a resume token to
  // figure out which documents in the client's cache are out of sync.
  BloomFilter unchanged_names = 3;
}
